// Enhanced type definitions for advanced analysis system

export interface ExpertAnalysis {
  expertType: 'photography' | 'psychology' | 'fashion' | 'data_science' | 'dating_coach';
  expertName: string;
  credentials: string;
  analysis: string;
  score: number;
  confidence: number;
  keyObservations: string[];
  recommendations: PrioritizedRecommendation[];
}

export interface PrioritizedRecommendation {
  recommendation: string;
  priority: 'high' | 'medium' | 'low';
  impactScore: number; // 0-100
  effortRequired: 'low' | 'medium' | 'high';
  category: string;
  reasoning: string;
}

export interface DetailedScoring {
  overallScore: number;
  subScores: {
    [category: string]: {
      score: number;
      weight: number;
      components: {
        [component: string]: number;
      };
    };
  };
  percentileRank: number;
  improvementPotential: number;
  marketCompetitiveness: number;
}

export interface ComparativeData {
  percentileRank: number;
  topPercentile: number; // What percentile this would be in
  averageScore: number;
  competitiveAdvantages: string[];
  areasForImprovement: string[];
  marketPosition: 'top_tier' | 'above_average' | 'average' | 'below_average' | 'needs_work';
}

export interface ConfidenceAnalysis {
  overallConfidence: number;
  confidenceFactors: {
    imageQuality?: number;
    analysisComplexity?: number;
    expertConsensus?: number;
    dataAvailability?: number;
  };
  uncertaintyAreas: string[];
  confidenceReasons: string[];
}

export interface AdvancedImageAnalysisResult {
  id: string;
  fileName: string;
  preview: string;
  timestamp: number;
  
  // Core Analysis
  overallScore: number;
  percentileRank: number;
  improvementPotential: number;
  marketCompetitiveness: number;
  
  // Expert Analyses
  expertAnalyses: ExpertAnalysis[];
  
  // Detailed Scoring
  detailedScoring: DetailedScoring;
  
  // Insights and Recommendations
  actionableInsights: PrioritizedRecommendation[];
  quickWins: PrioritizedRecommendation[];
  longTermImprovements: PrioritizedRecommendation[];
  
  // Comparative Analysis
  comparativeAnalysis: ComparativeData;
  
  // Confidence Metrics
  confidenceMetrics: ConfidenceAnalysis;
  
  // Demographic Analysis
  demographicInsights: {
    estimatedAge: string;
    targetAudience: string[];
    platformOptimization: {
      tinder: number;
      bumble: number;
      hinge: number;
    };
  };
  
  // Processing Info
  processingTime: number;
  modelUsed: string;
  analysisVersion: string;
}

export interface AdvancedBioAnalysisResult {
  id: string;
  originalBio: string;
  timestamp: number;
  
  // Core Analysis
  overallScore: number;
  percentileRank: number;
  improvementPotential: number;
  marketCompetitiveness: number;
  
  // Expert Analyses
  expertAnalyses: ExpertAnalysis[];
  
  // Detailed Scoring
  detailedScoring: DetailedScoring;
  
  // Linguistic Analysis
  linguisticAnalysis: {
    readabilityScore: number;
    sentimentScore: number;
    toneAnalysis: string[];
    vocabularyLevel: string;
    grammarScore: number;
  };
  
  // Psychological Profiling
  psychologicalProfile: {
    personalityTraits: {
      openness: number;
      conscientiousness: number;
      extraversion: number;
      agreeableness: number;
      neuroticism: number;
    };
    attachmentStyle: string;
    confidenceLevel: number;
    emotionalIntelligence: number;
  };
  
  // Market Analysis
  marketAnalysis: {
    targetAudienceAlignment: number;
    competitivePositioning: string;
    conversionPotential: number;
    engagementProbability: number;
    nichAppeal: string[];
  };
  
  // Insights and Recommendations
  actionableInsights: PrioritizedRecommendation[];
  quickWins: PrioritizedRecommendation[];
  longTermImprovements: PrioritizedRecommendation[];
  
  // Comparative Analysis
  comparativeAnalysis: ComparativeData;
  
  // Confidence Metrics
  confidenceMetrics: ConfidenceAnalysis;
  
  // Generated Improvements
  improvedVersions: {
    witty: string;
    sincere: string;
    adventurous: string;
  };
  
  // Processing Info
  processingTime: number;
  modelUsed: string;
  analysisVersion: string;
}

export interface AdvancedAnalysisProgress {
  phase: 'pre_analysis' | 'expert_analysis' | 'scoring' | 'insights' | 'comparison' | 'finalization';
  currentExpert?: string;
  progress: number;
  message: string;
  estimatedTimeRemaining?: number;
}

export interface AdvancedAnalysisConfig {
  targetDemographic?: 'young_adults' | 'professionals' | 'mature' | 'all';
  platform?: 'tinder' | 'bumble' | 'hinge' | 'general';
  analysisDepth?: 'standard' | 'comprehensive' | 'expert';
  includeComparative?: boolean;
  generateImprovements?: boolean;
}
