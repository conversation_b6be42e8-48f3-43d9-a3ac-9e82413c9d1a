/// <reference types="vite/client" />

import { openrouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";
import { AdvancedBioPromptGenerator } from "./prompts/bio-analysis-prompts";
import { AdvancedScoringEngine, DEFAULT_BIO_WEIGHTS, BIO_CATEGORY_WEIGHTS } from "./scoring/advanced-scoring";
import { getAllExpertTypes, getExpertCredentials } from "./types/expert-personas";
import type { 
  AdvancedBioAnalysisResult, 
  AdvancedAnalysisProgress, 
  AdvancedAnalysisConfig,
  ExpertAnalysis,
  PrioritizedRecommendation,
  ConfidenceAnalysis
} from "./types/advanced-analysis";

export class AdvancedBioAnalyzer {
  private apiKey: string;
  private promptGenerator: AdvancedBioPromptGenerator;
  private scoringEngine: AdvancedScoringEngine;

  constructor() {
    // Get API key from environment variables
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY;

    if (!this.apiKey) {
      console.error("🔑 VITE_OPENROUTER_API_KEY not found in environment variables");
      throw new Error(
        "OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file"
      );
    }

    console.log("🔑 OpenRouter API key loaded successfully for advanced bio analysis");
    console.log(`🔑 API Key preview: ${this.apiKey.substring(0, 10)}...`);

    // Set the API key for OpenRouter provider
    if (typeof globalThis !== "undefined") {
      (globalThis as any).process = (globalThis as any).process || {};
      (globalThis as any).process.env = (globalThis as any).process.env || {};
      (globalThis as any).process.env.OPENROUTER_API_KEY = this.apiKey;
    }

    this.promptGenerator = new AdvancedBioPromptGenerator();
    this.scoringEngine = new AdvancedScoringEngine();
  }

  async analyzeBio(
    bio: string,
    config: AdvancedAnalysisConfig = {},
    onProgress?: (progress: AdvancedAnalysisProgress) => void
  ): Promise<AdvancedBioAnalysisResult> {
    const startTime = Date.now();
    const analysisId = `bio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log(`🚀 Starting advanced bio analysis with o3`);
    console.log(`📝 Bio length: ${bio.length} characters`);

    try {
      // Phase 1: Linguistic Analysis
      onProgress?.({
        phase: 'pre_analysis',
        progress: 10,
        message: 'Performing linguistic analysis...'
      });

      const linguisticAnalysis = await this.performLinguisticAnalysis(bio);

      // Phase 2: Multi-expert analysis
      onProgress?.({
        phase: 'expert_analysis',
        progress: 20,
        message: 'Conducting multi-expert analysis...'
      });

      const expertAnalyses = await this.conductExpertAnalyses(bio, config, onProgress);

      // Phase 3: Psychological profiling
      onProgress?.({
        phase: 'scoring',
        progress: 70,
        message: 'Generating psychological profile...'
      });

      const psychologicalProfile = await this.generatePsychologicalProfile(bio, expertAnalyses);

      // Phase 4: Market analysis
      onProgress?.({
        phase: 'insights',
        progress: 80,
        message: 'Performing market analysis...'
      });

      const marketAnalysis = await this.performMarketAnalysis(bio, expertAnalyses);

      // Phase 5: Advanced scoring
      onProgress?.({
        phase: 'comparison',
        progress: 90,
        message: 'Calculating advanced scores...'
      });

      const detailedScoring = this.scoringEngine.calculateDetailedScoring(
        expertAnalyses,
        BIO_CATEGORY_WEIGHTS,
        DEFAULT_BIO_WEIGHTS
      );

      // Phase 6: Generate insights and improvements
      onProgress?.({
        phase: 'finalization',
        progress: 95,
        message: 'Generating improvements and insights...'
      });

      const actionableInsights = await this.generateActionableInsights(expertAnalyses, detailedScoring);
      const { quickWins, longTermImprovements } = this.categorizeRecommendations(actionableInsights);
      const improvedVersions = await this.generateImprovedVersions(bio, expertAnalyses);

      const comparativeAnalysis = this.scoringEngine.generateComparativeAnalysis(
        detailedScoring.overallScore,
        detailedScoring,
        expertAnalyses
      );

      const confidenceMetrics = this.calculateConfidenceMetrics(expertAnalyses, linguisticAnalysis);

      const processingTime = Date.now() - startTime;

      const result: AdvancedBioAnalysisResult = {
        id: analysisId,
        originalBio: bio,
        timestamp: Date.now(),
        
        // Core Analysis
        overallScore: detailedScoring.overallScore,
        percentileRank: detailedScoring.percentileRank,
        improvementPotential: detailedScoring.improvementPotential,
        marketCompetitiveness: detailedScoring.marketCompetitiveness,
        
        // Expert Analyses
        expertAnalyses,
        
        // Detailed Scoring
        detailedScoring,
        
        // Linguistic Analysis
        linguisticAnalysis,
        
        // Psychological Profiling
        psychologicalProfile,
        
        // Market Analysis
        marketAnalysis,
        
        // Insights and Recommendations
        actionableInsights,
        quickWins,
        longTermImprovements,
        
        // Comparative Analysis
        comparativeAnalysis,
        
        // Confidence Metrics
        confidenceMetrics,
        
        // Generated Improvements
        improvedVersions,
        
        // Processing Info
        processingTime,
        modelUsed: 'openai/o3',
        analysisVersion: '1.0.0'
      };

      console.log(`✅ Advanced bio analysis completed in ${processingTime}ms - Overall Score: ${result.overallScore}/100`);
      return result;

    } catch (error) {
      console.error("❌ Advanced bio analysis failed:", error);
      throw new Error(`Advanced analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async performLinguisticAnalysis(bio: string): Promise<any> {
    console.log("📊 Performing linguistic analysis...");
    
    const { text } = await generateText({
      model: openrouter("openai/o3"),
      messages: [
        {
          role: "system",
          content: `You are a computational linguistics expert. Analyze the following bio for linguistic characteristics and provide a JSON response with readability, sentiment, tone, vocabulary level, and grammar scores.`
        },
        {
          role: "user",
          content: `Analyze this bio linguistically:

"${bio}"

Provide analysis in this JSON format:
{
  "readabilityScore": 85,
  "sentimentScore": 72,
  "toneAnalysis": ["confident", "friendly", "authentic"],
  "vocabularyLevel": "intermediate",
  "grammarScore": 90
}`
        }
      ],
      maxTokens: 500,
      temperature: 0.1
    });

    return JSON.parse(text);
  }

  private async conductExpertAnalyses(
    bio: string,
    config: AdvancedAnalysisConfig,
    onProgress?: (progress: AdvancedAnalysisProgress) => void
  ): Promise<ExpertAnalysis[]> {
    const expertTypes = getAllExpertTypes();
    const expertAnalyses: ExpertAnalysis[] = [];
    
    for (let i = 0; i < expertTypes.length; i++) {
      const expertType = expertTypes[i];
      const progress = 20 + (i / expertTypes.length) * 45; // 20% to 65%
      
      onProgress?.({
        phase: 'expert_analysis',
        currentExpert: getExpertCredentials(expertType),
        progress,
        message: `Analyzing with ${expertType} expert...`
      });

      try {
        const analysis = await this.conductSingleExpertAnalysis(expertType, bio, config);
        expertAnalyses.push(analysis);
        
        console.log(`✅ ${expertType} analysis completed - Score: ${analysis.score}/100`);
        
        // Small delay between expert analyses
        if (i < expertTypes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 800));
        }
      } catch (error) {
        console.error(`❌ ${expertType} analysis failed:`, error);
        // Continue with other experts even if one fails
      }
    }

    return expertAnalyses;
  }

  private async conductSingleExpertAnalysis(
    expertType: string,
    bio: string,
    config: AdvancedAnalysisConfig
  ): Promise<ExpertAnalysis> {
    const prompt = this.promptGenerator.generateExpertPrompt(expertType, bio, config);
    
    console.log(`🤖 Calling OpenRouter with o3 for ${expertType} expert bio analysis`);

    const { text } = await generateText({
      model: openrouter("openai/o3"),
      messages: [
        {
          role: "system",
          content: prompt.systemPrompt
        },
        {
          role: "user",
          content: prompt.userPrompt
        }
      ],
      maxTokens: 1500,
      temperature: 0.1
    });

    // Parse the structured response
    const analysisData = JSON.parse(text);
    
    return {
      expertType: expertType as any,
      expertName: getExpertCredentials(expertType),
      credentials: getExpertCredentials(expertType),
      analysis: analysisData.expert_evaluation?.expert_specific_analysis || "Analysis completed",
      score: analysisData.scoring_methodology?.score || 50,
      confidence: analysisData.confidence_evaluation?.confidence || 80,
      keyObservations: analysisData.initial_assessment?.key_elements || [],
      recommendations: this.parseRecommendations(analysisData.strategic_recommendations || [])
    };
  }

  private parseRecommendations(recommendations: any[]): PrioritizedRecommendation[] {
    return recommendations.map(rec => ({
      recommendation: rec.recommendation || "No recommendation provided",
      priority: rec.impact_level === 'high' ? 'high' : rec.impact_level === 'medium' ? 'medium' : 'low',
      impactScore: this.mapImpactToScore(rec.impact_level),
      effortRequired: rec.implementation_difficulty === 'easy' ? 'low' : 
                     rec.implementation_difficulty === 'moderate' ? 'medium' : 'high',
      category: 'general',
      reasoning: rec.reasoning || "Expert recommendation"
    }));
  }

  private mapImpactToScore(impactLevel: string): number {
    switch (impactLevel) {
      case 'high': return 85;
      case 'medium': return 65;
      case 'low': return 40;
      default: return 50;
    }
  }

  private async generatePsychologicalProfile(bio: string, expertAnalyses: ExpertAnalysis[]): Promise<any> {
    console.log("🧠 Generating psychological profile...");
    
    const { text } = await generateText({
      model: openrouter("openai/o3"),
      messages: [
        {
          role: "system",
          content: `You are a clinical psychologist specializing in personality assessment. Analyze the bio for Big 5 personality traits, attachment style, confidence level, and emotional intelligence indicators.`
        },
        {
          role: "user",
          content: `Analyze this bio for psychological indicators:

"${bio}"

Provide analysis in this JSON format:
{
  "personalityTraits": {
    "openness": 75,
    "conscientiousness": 68,
    "extraversion": 82,
    "agreeableness": 71,
    "neuroticism": 25
  },
  "attachmentStyle": "secure",
  "confidenceLevel": 78,
  "emotionalIntelligence": 85
}`
        }
      ],
      maxTokens: 600,
      temperature: 0.1
    });

    return JSON.parse(text);
  }

  private async performMarketAnalysis(bio: string, expertAnalyses: ExpertAnalysis[]): Promise<any> {
    console.log("📈 Performing market analysis...");
    
    const { text } = await generateText({
      model: openrouter("openai/o3"),
      messages: [
        {
          role: "system",
          content: `You are a dating market analyst. Assess target audience alignment, competitive positioning, conversion potential, and niche appeal.`
        },
        {
          role: "user",
          content: `Analyze this bio for market positioning:

"${bio}"

Provide analysis in this JSON format:
{
  "targetAudienceAlignment": 82,
  "competitivePositioning": "above average",
  "conversionPotential": 75,
  "engagementProbability": 78,
  "nicheAppeal": ["professionals", "active lifestyle"]
}`
        }
      ],
      maxTokens: 500,
      temperature: 0.1
    });

    return JSON.parse(text);
  }

  private async generateActionableInsights(
    expertAnalyses: ExpertAnalysis[],
    detailedScoring: any
  ): Promise<PrioritizedRecommendation[]> {
    // Combine and prioritize recommendations from all experts
    const allRecommendations: PrioritizedRecommendation[] = [];
    
    for (const analysis of expertAnalyses) {
      allRecommendations.push(...analysis.recommendations);
    }

    // Sort by impact score and remove duplicates
    return allRecommendations
      .sort((a, b) => b.impactScore - a.impactScore)
      .slice(0, 8); // Top 8 recommendations
  }

  private categorizeRecommendations(recommendations: PrioritizedRecommendation[]): {
    quickWins: PrioritizedRecommendation[];
    longTermImprovements: PrioritizedRecommendation[];
  } {
    const quickWins = recommendations.filter(rec => 
      rec.effortRequired === 'low' && rec.impactScore >= 60
    );
    
    const longTermImprovements = recommendations.filter(rec => 
      rec.effortRequired === 'high' && rec.impactScore >= 70
    );

    return { quickWins, longTermImprovements };
  }

  private async generateImprovedVersions(bio: string, expertAnalyses: ExpertAnalysis[]): Promise<any> {
    console.log("✨ Generating improved bio versions...");
    
    const improvements = expertAnalyses
      .flatMap(analysis => analysis.recommendations)
      .slice(0, 5)
      .map(rec => rec.recommendation)
      .join('; ');

    const { text } = await generateText({
      model: openrouter("openai/o3"),
      messages: [
        {
          role: "system",
          content: `You are an expert dating profile writer. Create three improved versions of the bio in different tones: witty, sincere, and adventurous. Each should address the identified improvements while maintaining authenticity.`
        },
        {
          role: "user",
          content: `Original bio: "${bio}"

Key improvements needed: ${improvements}

Create three improved versions in this JSON format:
{
  "witty": "Improved witty version...",
  "sincere": "Improved sincere version...",
  "adventurous": "Improved adventurous version..."
}`
        }
      ],
      maxTokens: 800,
      temperature: 0.3
    });

    return JSON.parse(text);
  }

  private calculateConfidenceMetrics(expertAnalyses: ExpertAnalysis[], linguisticAnalysis: any): ConfidenceAnalysis {
    const confidences = expertAnalyses.map(a => a.confidence);
    const avgConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
    
    return {
      overallConfidence: Math.round(avgConfidence),
      confidenceFactors: {
        analysisComplexity: 85,
        expertConsensus: this.calculateExpertConsensus(expertAnalyses),
        dataAvailability: 90
      },
      uncertaintyAreas: this.identifyUncertaintyAreas(expertAnalyses),
      confidenceReasons: ["Multiple expert validation", "Comprehensive linguistic analysis", "Advanced psychological profiling"]
    };
  }

  private calculateExpertConsensus(expertAnalyses: ExpertAnalysis[]): number {
    const scores = expertAnalyses.map(a => a.score);
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    const standardDeviation = Math.sqrt(variance);
    
    return Math.max(0, Math.round(100 - (standardDeviation * 2)));
  }

  private identifyUncertaintyAreas(expertAnalyses: ExpertAnalysis[]): string[] {
    const uncertaintyAreas: string[] = [];
    
    const scoresByExpert = expertAnalyses.reduce((acc, analysis) => {
      acc[analysis.expertType] = analysis.score;
      return acc;
    }, {} as Record<string, number>);

    if (Math.abs(scoresByExpert.psychology - scoresByExpert.dating_coach) > 20) {
      uncertaintyAreas.push("Psychological appeal vs. practical dating advice alignment");
    }

    return uncertaintyAreas;
  }
}
